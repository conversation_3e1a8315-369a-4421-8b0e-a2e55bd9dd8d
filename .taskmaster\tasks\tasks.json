{"master": {"tasks": [{"id": 1, "title": "Define and Create `model-fallback-config.json`", "description": "Create the `model-fallback-config.json` file in the project's root directory, defining the ordered list of primary and fallback models, including identifiers for both Gemini API and Gemini CLI providers.", "details": "Create a new file named `model-fallback-config.json` at the project root. The file should contain a JSON array of strings, where each string is a model identifier.  `[\"gemini-2.5-pro\", \"gemini/gemini-2.5-pro\", \"gemini/gemini-2.5-flash\", \"gemini-2.5-flash\"]`. Ensure the identifiers clearly distinguish between Gemini CLI models (e.g., `gemini-2.5-pro`) and Gemini API models (e.g., `gemini/gemini-2.5-pro`).", "testStrategy": "Verify the `model-fallback-config.json` file exists in the correct location and contains a valid JSON array of model identifier strings as specified.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Create model-fallback-config.json", "description": "Create the `model-fallback-config.json` file with a JSON array structure, including example model identifiers for both Gemini API and CLI providers.", "dependencies": [], "details": "The file should contain a JSON array. Each object in the array should have `model_id` and `provider` keys. Example: `[{\"model_id\": \"gemini-pro\", \"provider\": \"gemini-api\"}, {\"model_id\": \"gemini-1.5-flash\", \"provider\": \"gemini-cli\"}]`", "status": "done", "testStrategy": ""}]}, {"id": 2, "title": "Update `Controller` to Load Configuration", "description": "Modify the `Controller` class (`src/core/controller/index.ts`) to read and parse the `model-fallback-config.json` file on application startup. Store the parsed model fallback chain in the Controller's internal state.", "details": "In `src/core/controller/index.ts`, implement a method (e.g., `loadModelFallbackConfig`) that reads `model-fallback-config.json` using Node.js `fs` module (e.g., `fs.readFileSync` and `JSON.parse`). Handle potential file not found or JSON parsing errors gracefully. Store the resulting array of model identifiers in a private class property (e.g., `private modelFallbackChain: string[]`). This method should be called during the Controller's initialization.", "testStrategy": "Unit test the `Controller`'s initialization to ensure it successfully loads and parses the `model-fallback-config.json` into its internal state. Verify that an invalid or missing file is handled without crashing.", "priority": "high", "dependencies": [1], "status": "done", "subtasks": [{"id": 1, "title": "Implement File Reading for Fallback Configuration", "description": "Create a dedicated method or utility function to read the `model-fallback-config.json` file from a specified path. Implement initial error handling to gracefully manage scenarios where the file does not exist, logging a warning and returning an indication that the file could not be found.", "dependencies": [], "details": "Determine the absolute or relative path for `model-fallback-config.json`. Use appropriate file I/O operations (e.g., `fs.readFile` in Node.js, `open()` in Python). The function should return the file content as a string or null/undefined if not found.\n<info added on 2025-07-04T22:00:24.507Z>\nTest Strategy for Scenario 2: Ensure no `model-fallback-config.json` file is present in the mocked environment. Instantiate the `Controller`. Verify that `_modelFallbackConfig` is `undefined`. Check that a warning message similar to `[WARN] model-fallback-config.json not found or invalid at [path]. Using default configuration.` is logged to the console.\n</info added on 2025-07-04T22:00:24.507Z>", "status": "done", "testStrategy": ""}, {"id": 2, "title": "Parse Configuration JSON and Handle Invalid Format", "description": "Extend the file reading logic (or create a subsequent step) to parse the content retrieved from `model-fallback-config.json` as JSON. Implement robust error handling for cases where the file content is not valid JSON, logging an error and ensuring the parsing process does not crash the application, returning a default empty configuration instead.", "dependencies": [1], "details": "Utilize a JSON parsing library (e.g., `JSON.parse()` in JavaScript, `json.loads()` in Python). Catch parsing exceptions (e.g., `SyntaxError`). If parsing fails, return an empty object `{}` or `null` to signify no valid configuration.\n<info added on 2025-07-04T22:00:41.103Z>\nMock a `model-fallback-config.json` file with malformed JSON content. Instantiate the `Controller`. Verify that `_modelFallbackConfig` is `undefined`. Check that an error message similar to `Error parsing JSON file at [path]:` is logged to the console.\n</info added on 2025-07-04T22:00:41.103Z>", "status": "done", "testStrategy": ""}, {"id": 3, "title": "Integrate Configuration Loading into Controller Initialization", "description": "Modify the Controller's initialization method (e.g., constructor or `init` method) to call the file reading and JSON parsing logic. Store the successfully parsed configuration data in a dedicated internal state variable (e.g., `this._fallbackConfig` or `self._fallback_config`). Ensure that if any errors occur during loading (file not found, invalid JSON), the internal state variable is set to a default empty state (e.g., an empty object or `null`) and the Controller initializes successfully without critical errors.", "dependencies": [1, 2], "details": "Call the functions/methods developed in subtasks 1 and 2 within the Controller's initialization flow. Define the default value for the internal configuration state. Add logging for successful loading and all error scenarios.", "status": "done", "testStrategy": ""}, {"id": 4, "title": "Unit Test Controller Initialization - Scenario 1: <PERSON><PERSON> config", "description": "Mock a `model-fallback-config.json` file with valid JSON content. Instantiate the `Controller`. Verify that `_modelFallbackConfig` in the `Controller`'s internal state correctly reflects the parsed content of the mocked file. Check that no warnings or errors related to file loading are logged.", "details": "", "status": "in-progress", "dependencies": [], "parentTaskId": 2}, {"id": 5, "title": "Unit Test Controller Initialization - Scenario 2: Missing config", "description": "Ensure no `model-fallback-config.json` file is present in the mocked environment. Instantiate the `Controller`. Verify that `_modelFallbackConfig` is `undefined`. Check that a warning message similar to `[WARN] model-fallback-config.json not found or invalid at [path]. Using default configuration.` is logged to the console.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 2}, {"id": 6, "title": "Unit Test Controller Initialization - Scenario 3: Invalid JSON format", "description": "Mock a `model-fallback-config.json` file with malformed JSON content. Instantiate the `Controller`. Verify that `_modelFallbackConfig` is `undefined`. Check that an error message similar to `Error parsing JSON file at [path]:` is logged to the console.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 2}]}, {"id": 3, "title": "Implement `Controller` Model Management Logic", "description": "Implement methods within the `Controller` class to manage the currently active model, track its index in the fallback chain, and provide a mechanism to advance to the next model.", "details": "Add properties to `Controller` like `private currentModelIndex: number = 0;` and `private activeModelIdentifier: string;`. Implement methods such as `getCurrentModelIdentifier(): string` which returns `this.modelFallbackChain[this.currentModelIndex]`, and `advanceToNextModel(): boolean` which increments `currentModelIndex` and updates `activeModelIdentifier`. This method should return `true` if a next model is available, `false` otherwise (indicating exhaustion). Also, consider a `resetModelChain()` method for new tasks.", "testStrategy": "Unit test the `Controller`'s model management methods. Verify `getCurrentModelIdentifier` returns the correct model, `advanceToNextModel` correctly moves to the next model and returns `false` when the end of the chain is reached. Test resetting the chain.", "priority": "high", "dependencies": [2], "status": "done", "subtasks": [{"id": 1, "title": "Define Model Management Private Properties", "description": "Add the necessary private properties `currentModelIndex` (e.g., `private int currentModelIndex;`) and `activeModelIdentifier` (e.g., `private String activeModelIdentifier;`) to the `Controller` class to manage the state of the active model and its position in the fallback chain.", "dependencies": [], "details": "These properties will store the current index in the model fallback list and the identifier of the currently active model.", "status": "done", "testStrategy": ""}, {"id": 2, "title": "Implement `getCurrentModelIdentifier()` Method", "description": "Create a public method `String getCurrentModelIdentifier()` in the `Controller` class that returns the value of the `activeModelIdentifier` private property. This method should provide direct access to the currently selected model's identifier.", "dependencies": [1], "details": "This method is a simple getter for the `activeModelIdentifier` property.", "status": "done", "testStrategy": ""}, {"id": 3, "title": "Implement `advanceToNextModel()` Method", "description": "Create a public method `boolean advanceToNextModel()` in the `Controller` class. This method should increment `currentModelIndex`, update `activeModelIdentifier` to the next model in the predefined fallback chain, and include robust boundary checks to ensure it doesn't go out of bounds. It should return `true` if a new model was successfully advanced to, and `false` if there are no more models in the chain.", "dependencies": [1], "details": "This method manages the core logic for switching to the next available model, handling the end-of-chain scenario.", "status": "done", "testStrategy": ""}, {"id": 4, "title": "Implement `resetModel<PERSON>hain()` Method", "description": "Create a public method `void resetModel<PERSON>hain()` in the `Controller` class. This method should reset `currentModelIndex` to its initial state (e.g., 0) and update `activeModelIdentifier` to the identifier of the first model in the fallback chain, effectively restarting the model selection process.", "dependencies": [1], "details": "This method provides a way to revert to the primary model or the beginning of the fallback sequence.", "status": "done", "testStrategy": ""}]}, {"id": 4, "title": "Refactor `Task.attemptApiRequest` for Error Handling", "description": "Refactor the `attemptApiRequest` method in `src/core/task/index.ts` to wrap API calls in a `try-catch` block, enabling robust error interception, specifically for HTTP errors.", "details": "Locate the `attemptApiRequest` method in `src/core/task/index.ts`. Wrap the actual API call (e.g., to the Gemini provider) within a `try-catch` block. The `catch` block should be designed to capture network errors, HTTP response errors, and specific API client errors. Ensure the caught error object provides access to HTTP status codes (e.g., `error.response.status` or `error.statusCode`) and error messages.", "testStrategy": "Simulate an API call that results in an error (e.g., a network timeout or a generic HTTP 500). Verify that the `catch` block in `attemptApiRequest` is correctly triggered and that the error object contains relevant information like status code and message.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Implement Core Try-Catch Structure", "description": "Wrap the existing API request logic within `Task.attemptApiRequest` with a `try-catch` block to intercept potential errors during the API call. Initially, ensure a generic error is caught and logged or re-thrown.", "dependencies": [], "details": "This step focuses on the foundational change to introduce error handling.", "status": "done", "testStrategy": ""}, {"id": 2, "title": "Refine Error Interception and Detail Extraction", "description": "Within the `catch` block, implement logic to differentiate between network errors, HTTP response errors (e.g., 4xx, 5xx status codes), and specific API client errors. Ensure the caught error object is transformed or augmented to consistently provide access to HTTP status codes and detailed error messages for all intercepted error types.", "dependencies": [1], "details": "This step focuses on the robustness and detail of the error handling, ensuring specific error types are identified and their relevant data (status code, message) is accessible.", "status": "done", "testStrategy": ""}]}, {"id": 5, "title": "Implement Rate-Limit Detection in `Task`", "description": "Within the `catch` block of `Task.attemptApiRequest`, implement logic to specifically detect rate-limit errors based on HTTP `429` status codes and known rate-limit error messages from Gemini and Gemini CLI API providers.", "details": "Inside the `catch` block, add conditional logic to check for rate-limit indicators. This should include checking `error.statusCode === 429`. Additionally, parse the error message or response body for specific strings indicating rate limits (e.g., `error.message.includes('rate limit exceeded')`, `error.message.includes('quota exceeded')`). This logic should be precise to avoid misinterpreting other errors as rate limits.", "testStrategy": "Mock API responses to return an HTTP `429` status code with various rate-limit specific messages. Verify that the detection logic correctly identifies these as rate-limit errors and distinguishes them from other types of errors (e.g., 401, 500).", "priority": "high", "dependencies": [4], "status": "done", "subtasks": [{"id": 1, "title": "Implement HTTP 429 Status Code Check", "description": "Add logic within the `catch` block of `Task.attemptApiRequest` to specifically check if the caught error object indicates an HTTP status code of `429 Too Many Requests`. This should be the primary and most direct rate-limit detection mechanism.", "dependencies": [], "details": "Focus on accessing the error's response status property (e.g., `error.response.status` or similar depending on the HTTP client library).\n<info added on 2025-07-04T22:01:10.989Z>\nTest Strategy for Scenario 1: Mock the `ApiHandler.createMessage` to throw an error object with `statusCode: 429`. Call `attemptApiRequest`. Verify that a `console.warn` message indicating `Rate limit detected (HTTP 429)` is logged.\n</info added on 2025-07-04T22:01:10.989Z>", "status": "done", "testStrategy": ""}, {"id": 2, "title": "Check for Gemini API Specific Rate Limit Messages", "description": "Extend the error handling to parse the error response body or message for specific string patterns indicating a rate limit from the Gemini API. This is crucial for cases where a 429 might not be explicitly returned, or for more granular detection.", "dependencies": [1], "details": "Identify common rate-limit messages from Gemini API documentation or observed errors (e.g., 'quota exceeded', 'rate limit exceeded'). This check should ideally follow the HTTP status code check.\n<info added on 2025-07-04T22:01:24.675Z>\nTest Strategy for Scenario 2: Mock the `ApiHandler.createMessage` to throw an error object with `message` containing `'rate limit exceeded'` or `'quota exceeded'`. Call `attemptApiRequest`. Verify that a `console.warn` message indicating `Gemini API rate limit detected` is logged.\n</info added on 2025-07-04T22:01:24.675Z>", "status": "done", "testStrategy": ""}, {"id": 3, "title": "Check for Gemini CLI Specific Rate Limit Messages", "description": "Implement additional parsing within the error handling to identify rate-limit messages originating specifically from the Gemini CLI. The CLI might wrap or reformat API errors, requiring different string pattern matching.", "dependencies": [1], "details": "Research or observe typical rate-limit error messages output by the Gemini CLI. This might involve looking for 'CLI' specific error codes or phrasing that differs from direct API responses.\n<info added on 2025-07-04T22:01:36.288Z>\nTest Strategy for Scenario 3: Mock the `ApiHandler.createMessage` to throw an error object with `message` containing `'Gemini CLI'` and `'rate limit'` or `'quota'`. Call `attemptApiRequest`. Verify that a `console.warn` message indicating `Gemini CLI rate limit detected` is logged.\n</info added on 2025-07-04T22:01:36.288Z>", "status": "done", "testStrategy": ""}, {"id": 4, "title": "Unit Test attemptApiRequest - Scenario 1: HTTP 429", "description": "Mock the `ApiHandler.createMessage` to throw an error object with `statusCode: 429`. Call `attemptApiRequest`. Verify that a `console.warn` message indicating `Rate limit detected (HTTP 429)` is logged.", "details": "", "status": "in-progress", "dependencies": [], "parentTaskId": 5}, {"id": 5, "title": "Unit Test attemptApiRequest - Scenario 2: Gemini API rate limit message", "description": "Mock the `ApiHandler.createMessage` to throw an error object with `message` containing `'rate limit exceeded'` or `'quota exceeded'`. Call `attemptApiRequest`. Verify that a `console.warn` message indicating `Gemini API rate limit detected` is logged.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 5}, {"id": 6, "title": "Unit Test attemptApiRequest - Scenario 3: Gemini CLI rate limit message", "description": "Mock the `ApiHandler.createMessage` to throw an error object with `message` containing `'Gemini CLI'` and `'rate limit'` or `'quota'`. Call `attemptApiRequest`. Verify that a `console.warn` message indicating `Gemini CLI rate limit detected` is logged.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 5}, {"id": 7, "title": "Unit Test attemptApiRequest - Scenario 4: Other errors", "description": "Mock the `ApiHandler.createMessage` to throw a generic error without rate-limit indicators. Call `attemptApiRequest`. Verify that none of the rate-limit `console.warn` messages are logged.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 5}]}, {"id": 6, "title": "Implement Automated Model Switching and Retry Logic", "description": "Upon detecting a rate-limit error, implement the automated model switching logic in `Task.attemptApiRequest`. This involves calling the `Controller` to advance to the next model and then recursively retrying the API request with the newly selected model.", "details": "If a rate-limit error is detected, call `this.controller.advanceToNextModel()`. If `advanceToNextModel()` returns `true` (meaning a fallback model is available), then recursively call `return this.attemptApiRequest()` to retry the operation. Ensure that the `ApiHandler` instance used for the API request is dynamically created or updated based on the `this.controller.getCurrentModelIdentifier()` to use the correct Gemini provider (Gemini API or Gemini CLI).", "testStrategy": "Conduct an end-to-end test where the primary model is configured to return a `429` rate-limit error. Verify that the system automatically switches to the next configured fallback model and retries the request successfully without user intervention.", "priority": "high", "dependencies": [3, 5], "status": "pending", "subtasks": [{"id": 1, "title": "Implement Initial Model Advancement Trigger", "description": "Modify `Task.attemptApiRequest` to include logic for detecting API errors (e.g., rate limits, specific error codes) that warrant a model switch or retry. If such an error occurs, call `this.controller.advanceToNextModel()` to request the next available model.", "dependencies": [], "details": "This subtask focuses on the initial check and the first call to the controller's model advancement method.", "status": "in-progress", "testStrategy": ""}, {"id": 2, "title": "Handle Model Advancement Failure/Exhaustion", "description": "After calling `this.controller.advanceToNextModel()`, check its boolean return value. If it returns `false`, it signifies that no more models are available for retry or the retry limit has been reached. In this scenario, the `attemptApiRequest` should terminate the retry loop and propagate a final error indicating model exhaustion.", "dependencies": [1], "details": "This ensures the system doesn't enter an infinite loop if no more models can be provided.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Dynamically Update ApiHandler Instance", "description": "If `this.controller.advanceToNextModel()` returns `true`, retrieve the identifier for the newly selected model using `this.controller.getCurrentModelIdentifier()`. Based on this identifier, dynamically create a new `ApiHandler` instance or update the existing one to reflect the configuration of the new model.", "dependencies": [2], "details": "This step ensures the API requests are made using the correct handler for the new model.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Implement Recursive Retry with New Model", "description": "After successfully updating the `<PERSON><PERSON><PERSON><PERSON><PERSON>` instance with the new model's configuration, recursively call `this.attemptApiRequest()` again. Ensure that the context (e.g., the `<PERSON><PERSON><PERSON><PERSON>ler` instance) for this recursive call correctly uses the newly configured model.", "dependencies": [3], "details": "This is the core of the retry mechanism, re-attempting the original request with the new model.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Refine Overall Error Handling and Task State", "description": "Review and refine the comprehensive error handling within `attemptApiRequest` to gracefully manage all failure scenarios, including when all models have been attempted and failed, or when the maximum number of retries (across all models) has been exhausted. Ensure appropriate logging and that the `Task`'s final state accurately reflects the outcome.", "dependencies": [4], "details": "This ensures robustness and proper reporting of task failures after exhausting all retry options.", "status": "pending", "testStrategy": ""}]}, {"id": 7, "title": "Implement Infinite Loop Prevention in `Task`", "description": "Modify the `Task` class to keep a record of models that have already failed due to rate-limiting for the *current* API request, preventing infinite retry loops on exhausted models.", "details": "Add a mechanism to `Task` (e.g., a `Set<string>` named `failedModelsForCurrentRequest`) to store identifiers of models that have already rate-limited during the current `attemptApiRequest` call sequence. Before retrying with a new model, check if `this.controller.getCurrentModelIdentifier()` is already in `failedModelsForCurrentRequest`. If it is, or if `advanceToNextModel()` returns `false` (no more fallbacks), then trigger the exhaustion scenario instead of retrying.", "testStrategy": "Simulate a scenario where the primary model rate-limits, then the first fallback model rate-limits. Verify that the system correctly identifies that all available models have been exhausted for the current request and does not enter an infinite loop by retrying already failed models.", "priority": "high", "dependencies": [6], "status": "pending", "subtasks": [{"id": 1, "title": "Introduce `failedModels` Set in `Task`", "description": "Add a new private `Set<string>` property, e.g., `_failedModelsForCurrentRequest`, to the `Task` class. This set will store identifiers of models that have failed due to rate-limiting for the current API request sequence. Ensure it's initialized appropriately for each new request.", "dependencies": [], "details": "Modify the `Task` class definition to include `private _failedModelsForCurrentRequest: Set<string>;` and ensure its lifecycle (e.g., clearing or re-initialization) aligns with the start of a new API request sequence.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Populate `failedModels` on Rate-Limit Failure", "description": "Modify the `Task`'s error handling logic to add a model's identifier to the `_failedModelsForCurrentRequest` set whenever an API call using that model fails specifically due to a rate-limiting error.", "dependencies": [1], "details": "Identify the specific error codes or conditions that signify a rate-limiting failure. Within the `Task`'s retry or error handling mechanism, if such an error occurs, add the `modelId` to `this._failedModelsForCurrentRequest`.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Modify Model Selection to <PERSON><PERSON> Failed Models", "description": "Update the `Task`'s model selection or iteration logic to prevent re-attempting models that are already present in the `_failedModelsForCurrentRequest` set for the current API request sequence.", "dependencies": [1, 2], "details": "Before attempting to use a model, check if `this._failedModelsForCurrentRequest.has(modelId)` returns true. If it does, skip this model and proceed to the next available model in the sequence.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Implement Termination for Exhausted Models", "description": "Add a final check to the `Task`'s execution loop to detect when all available models (or all models not in `_failedModelsForCurrentRequest`) have been attempted and failed. If this condition is met, terminate the `Task` gracefully to prevent an infinite loop.", "dependencies": [3], "details": "Ensure that if the `Task` iterates through all its configured models and finds that all of them are either in `_failedModelsForCurrentRequest` or have otherwise failed, it exits the retry loop and reports a final failure, rather than continuing to retry indefinitely.", "status": "pending", "testStrategy": ""}]}, {"id": 8, "title": "Implement User Notification System", "description": "Integrate a user notification system to inform the user via the chat interface when a rate limit is encountered and a fallback model is being used.", "details": "When a rate-limit error is detected and a fallback is initiated, use Cline's existing UI messaging utility (e.g., `this.ui.postMessage`) to send a message to the chat. The message should clearly state which model was rate-limited and which fallback model is now being used, including its provider (e.g., 'Primary model is rate-limited. Switching to fallback: `gemini/gemini-2.5-pro` (Gemini API).').", "testStrategy": "Perform a test where a rate limit occurs and a fallback is triggered. Visually inspect the Cline UI to ensure the correct notification message appears in the chat, accurately reflecting the model switch and provider.", "priority": "medium", "dependencies": [6], "status": "pending", "subtasks": [{"id": 1, "title": "Identify and Integrate with Existing UI Messaging Utility", "description": "Locate and understand the usage of the existing UI messaging utility (e.g., `this.ui.postMessage`) to ensure proper integration for displaying user notifications.", "dependencies": [], "details": "This involves confirming the exact method signature, required parameters, and expected behavior of the `postMessage` utility within the current UI framework.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Craft and Trigger Informative Rate-Limit Fallback Messages", "description": "Develop clear and informative notification messages that specify which model was rate-limited, which fallback model is now being used, and its provider. Implement the logic to trigger these messages via the identified UI utility whenever a model switch due to rate-limiting occurs.", "dependencies": [1], "details": "Messages should be user-friendly, e.g., 'Model X was rate-limited. Falling back to Model Y (Provider Z).'. Ensure the triggering mechanism is robust and fires reliably upon model switching events.", "status": "pending", "testStrategy": ""}]}, {"id": 9, "title": "Implement Exhaustion Scenario Handling", "description": "Implement the exhaustion scenario handling: if all models in the fallback chain are rate-limited for a given request, notify the user and prompt them to manually retry the last attempted model.", "details": "When `Task` determines that no more fallback models are available (either `Controller.advanceToNextModel()` returns `false` or all models in `failedModelsForCurrentRequest` have been exhausted), post a specific message to the UI. This message should inform the user that all fallbacks have failed and prompt them to manually retry the last attempted model (e.g., 'All fallback models rate-limited. Please retry manually with `gemini-2.5-flash` (Gemini CLI).'). The task should then terminate gracefully, potentially returning an error that the UI can interpret for the manual retry prompt.", "testStrategy": "Configure a `model-fallback-config.json` with a short list of models. Simulate rate-limit errors for all models in the list. Verify that the system correctly identifies the exhaustion scenario and displays the appropriate user notification prompting for manual retry, without entering an infinite loop.", "priority": "high", "dependencies": [7, 8], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-06-30T15:17:25.982Z", "updated": "2025-07-06T01:33:08.426Z", "description": "Tasks for master context"}}}