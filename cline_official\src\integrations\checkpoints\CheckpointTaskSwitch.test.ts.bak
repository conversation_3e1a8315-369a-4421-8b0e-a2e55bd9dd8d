import { expect } from "chai"
import { describe, it, beforeEach, afterEach } from "mocha"
import fs from "fs/promises"
import { createTestEnvironment, createTestTracker } from "./Checkpoint-test-utils"
import { HistoryItem } from "../../shared/HistoryItem"
import CheckpointTracker from "./CheckpointTracker"

describe("Checkpoint Task Switching", () => {
    let env: Awaited<ReturnType<typeof createTestEnvironment>>
    let taskId1: string
    let taskId2: string
    let tracker1: CheckpointTracker | undefined
    let tracker2: CheckpointTracker | undefined

    beforeEach(async () => {
        env = await createTestEnvironment()
        taskId1 = "task-1"
        taskId2 = "task-2"
        tracker1 = await createTestTracker(env.globalStoragePath, taskId1)
        if (!tracker1) {throw new Error("Failed to create tracker1")}
    })

    afterEach(async () => {
        await env.cleanup()
    })

    it("should maintain separate history for each task", async () => {
        if (!tracker1) {throw new Error("Failed to create tracker1")}
        
        // Create and commit file in first task
        await fs.writeFile(env.testFilePath, "task1 initial")
        const task1Commit1 = await tracker1.commit()
        expect(task1Commit1).to.be.a("string").and.not.empty

        // Modify and commit again in first task
        await fs.writeFile(env.testFilePath, "task1 modified")
        const task1Commit2 = await tracker1.commit()
        expect(task1Commit2).to.be.a("string").and.not.empty

        // Create second task tracker
        tracker2 = await createTestTracker(env.globalStoragePath, taskId2)
        if (!tracker2) {throw new Error("Failed to create tracker2")}

        // Create and commit file in second task
        await fs.writeFile(env.testFilePath, "task2 initial")
        const task2Commit1 = await tracker2.commit()
        expect(task2Commit1).to.be.a("string").and.not.empty

        // Create another commit to establish history
        await fs.writeFile(env.testFilePath, "task2 modified")
        const task2Commit2 = await tracker2.commit()
        expect(task2Commit2).to.be.a("string").and.not.empty

        // Verify second task's history
        const task2Diff = await tracker2.getDiffSet(task2Commit1, task2Commit2)
        expect(task2Diff).to.have.lengthOf(1)
        expect(task2Diff[0].before).to.equal("task2 initial")
        expect(task2Diff[0].after).to.equal("task2 modified")

        // Switch back to first task by creating new tracker
        const tracker1Again = await createTestTracker(env.globalStoragePath, taskId1)
        if (!tracker1Again) {throw new Error("Failed to create tracker1Again")}

        // Verify first task's history is preserved
        const task1Diff = await tracker1Again.getDiffSet(task1Commit1, task1Commit2)
        expect(task1Diff[0].before).to.equal("task1 initial")
        expect(task1Diff[0].after).to.equal("task1 modified")

        // Reset first task to initial state
        if (!task1Commit1) {throw new Error("Failed to create initial commit")}
        await tracker1Again.resetHead(task1Commit1)
        const resetContent = await fs.readFile(env.testFilePath, "utf8")
        expect(resetContent).to.equal("task1 initial")
    })

    it("should handle task deletion and recreation", async () => {
        if (!tracker1) {throw new Error("Failed to create tracker1")}

        // Create and commit file in first task
        await fs.writeFile(env.testFilePath, "task1 content")
        const task1Commit = await tracker1.commit()
        expect(task1Commit).to.be.a("string").and.not.empty

        // Create second task
        tracker2 = await createTestTracker(env.globalStoragePath, taskId2)
        if (!tracker2) {throw new Error("Failed to create tracker2")}
        await fs.writeFile(env.testFilePath, "task2 content")
        const task2Commit = await tracker2.commit()
        expect(task2Commit).to.be.a("string").and.not.empty

        // Delete second task's checkpoints
        const historyItem: HistoryItem = {
            id: `test-${Date.now()}`,
            ts: Date.now(),
            task: taskId2,
            shadowGitConfigWorkTree: env.tempDir,
            tokensIn: 0,
            tokensOut: 0,
            totalCost: 0,
        }
        await CheckpointTracker.deleteCheckpoints(taskId2, historyItem, env.globalStoragePath)

        // Recreate second task
        const tracker2Again = await createTestTracker(env.globalStoragePath, taskId2)
        if (!tracker2Again) {throw new Error("Failed to create tracker2Again")}

        // Create new commit in recreated task
        await fs.writeFile(env.testFilePath, "task2 new content")
        const newCommit = await tracker2Again.commit()
        expect(newCommit).to.be.a("string").and.not.empty

        // Switch back to first task and verify its history is intact
        const tracker1Again = await createTestTracker(env.globalStoragePath, taskId1)
        if (!tracker1Again) {throw new Error("Failed to create tracker1Again")}
        if (!task1Commit) {throw new Error("Failed to create initial commit")}
        await tracker1Again.resetHead(task1Commit)
        const resetContent = await fs.readFile(env.testFilePath, "utf8")
        expect(resetContent).to.equal("task1 content")
    })
})
