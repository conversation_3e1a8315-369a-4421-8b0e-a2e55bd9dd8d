import { expect } from "chai"
import { describe, it, beforeEach, afterEach } from "mocha"
import fs from "fs/promises"
import path from "path"
import { createTestEnvironment, createTestTracker } from "./Checkpoint-test-utils"

describe("Checkpoint Commit Operations", () => {
    let env: Awaited<ReturnType<typeof createTestEnvironment>>

    beforeEach(async () => {
        env = await createTestEnvironment()
    })

    afterEach(async () => {
        await env.cleanup()
    })

    it("should create commit with single file changes", async () => {
        const tracker = await createTestTracker(env.globalStoragePath)
        if (!tracker) {throw new Error("Failed to create tracker")}

        // Create initial file
        await fs.writeFile(env.testFilePath, "initial content")

        // Create first commit
        const firstCommit = await tracker.commit()
        expect(firstCommit).to.be.a("string").and.not.empty

        // Modify file
        await fs.writeFile(env.testFilePath, "modified content")

        // Create second commit
        const secondCommit = await tracker.commit()
        expect(secondCommit).to.be.a("string").and.not.empty
        expect(secondCommit).to.not.equal(firstCommit)

        // Verify commits are different
        const diffSet = await tracker.getDiffSet(firstCommit, secondCommit)
        expect(diffSet).to.have.lengthOf(1)
        expect(diffSet[0].before).to.equal("initial content")
        expect(diffSet[0].after).to.equal("modified content")
    })

    it("should create commit with multiple file changes", async () => {
        const tracker = await createTestTracker(env.globalStoragePath)
        if (!tracker) {throw new Error("Failed to create tracker")}

        // Create initial files with newlines
        const testFile2Path = path.join(env.tempDir, "src", "test2.txt")
        await fs.writeFile(env.testFilePath, "file1 initial\n")
        await fs.writeFile(testFile2Path, "file2 initial\n")

        // Create first commit
        const firstCommit = await tracker.commit()
        expect(firstCommit).to.be.a("string").and.not.empty

        // Modify both files with newlines
        await fs.writeFile(env.testFilePath, "file1 modified\n")
        await fs.writeFile(testFile2Path, "file2 modified\n")

        // Create second commit
        const secondCommit = await tracker.commit()
        expect(secondCommit).to.be.a("string").and.not.empty
        expect(secondCommit).to.not.equal(firstCommit)

        // Get diff between commits
        const diffSet = await tracker.getDiffSet(firstCommit, secondCommit)
        expect(diffSet).to.have.lengthOf(2)

        // Sort diffSet by path for consistent ordering
        const sortedDiffs = diffSet.sort((a, b) => a.relativePath.localeCompare(b.relativePath))

        // Verify file paths
        expect(sortedDiffs[0].relativePath).to.equal("src/test.txt")
        expect(sortedDiffs[1].relativePath).to.equal("src/test2.txt")

        // Verify file contents
        expect(sortedDiffs[0].before).to.equal("file1 initial\n")
        expect(sortedDiffs[0].after).to.equal("file1 modified\n")
        expect(sortedDiffs[1].before).to.equal("file2 initial\n")
        expect(sortedDiffs[1].after).to.equal("file2 modified\n")
    })

    it("should create commit when files are deleted", async () => {
        const tracker = await createTestTracker(env.globalStoragePath)
        if (!tracker) {throw new Error("Failed to create tracker")}

        // Create and commit initial file
        await fs.writeFile(env.testFilePath, "initial content")
        const firstCommit = await tracker.commit()
        expect(firstCommit).to.be.a("string").and.not.empty

        // Delete file
        await fs.unlink(env.testFilePath)

        // Create second commit
        const secondCommit = await tracker.commit()
        expect(secondCommit).to.be.a("string").and.not.empty
        expect(secondCommit).to.not.equal(firstCommit)

        // Verify file deletion was committed
        const diffSet = await tracker.getDiffSet(firstCommit, secondCommit)
        expect(diffSet).to.have.lengthOf(1)
        expect(diffSet[0].before).to.equal("initial content")
        expect(diffSet[0].after).to.equal("")
    })

    it("should create empty commit when no changes", async () => {
        const tracker = await createTestTracker(env.globalStoragePath)
        if (!tracker) {throw new Error("Failed to create tracker")}

        // Create and commit initial file
        await fs.writeFile(env.testFilePath, "test content")
        const firstCommit = await tracker.commit()
        expect(firstCommit).to.be.a("string").and.not.empty

        // Create commit without changes
        const secondCommit = await tracker.commit()
        expect(secondCommit).to.be.a("string").and.not.empty
        expect(secondCommit).to.not.equal(firstCommit)

        // Verify no changes between commits
        const diffSet = await tracker.getDiffSet(firstCommit, secondCommit)
        expect(diffSet).to.have.lengthOf(0)
    })

    it("should handle files in nested directories", async () => {
        const tracker = await createTestTracker(env.globalStoragePath)
        if (!tracker) {throw new Error("Failed to create tracker")}

        // Create nested directory structure
        const nestedDir = path.join(env.tempDir, "src", "deep", "nested")
        await fs.mkdir(nestedDir, { recursive: true })
        const nestedFilePath = path.join(nestedDir, "nested.txt")

        // Create and commit file in nested directory
        await fs.writeFile(nestedFilePath, "nested content")
        const firstCommit = await tracker.commit()
        expect(firstCommit).to.be.a("string").and.not.empty

        // Modify nested file
        await fs.writeFile(nestedFilePath, "modified nested content")

        // Create second commit
        const secondCommit = await tracker.commit()
        expect(secondCommit).to.be.a("string").and.not.empty

        // Verify changes were committed
        const diffSet = await tracker.getDiffSet(firstCommit, secondCommit)
        expect(diffSet).to.have.lengthOf(1)
        expect(diffSet[0].relativePath).to.equal("src/deep/nested/nested.txt")
        expect(diffSet[0].before).to.equal("nested content")
        expect(diffSet[0].after).to.equal("modified nested content")
    })
})
