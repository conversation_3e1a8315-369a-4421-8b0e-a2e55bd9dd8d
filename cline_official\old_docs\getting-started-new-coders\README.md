# Getting Started with <PERSON><PERSON> | New Coders

Welcome to Cline! This guide will help you get set up and start using Cline to build your first project.

## What You'll Need

Before you begin, make sure you have the following:

-   **VS Code:** A free, powerful code editor.
    -   [Download VS Code](https://code.visualstudio.com/)
-   **Development Tools:** Essential software for coding (Homebrew, Node.js, Git, etc.).
    -   Follow our [Installing Essential Development Tools](installing-dev-essentials.md) guide to set these up with <PERSON><PERSON>'s help (after getting setup here)
    -   <PERSON>line will guide you through installing everything you need
-   **Cline Projects Folder:** A dedicated folder for all your Cline projects.
    -   On macOS: Create a folder named "<PERSON>line" in your Documents folder
        -   Path: `/Users/<USER>/Documents/Cline`
    -   On Windows: Create a folder named "<PERSON>line" in your Documents folder
        -   Path: `C:\Users\<USER>\Documents\Cline`
    -   Inside this Cline folder, create separate folders for each project
        -   Example: `Documents/Cline/workout-app` for a workout tracking app
        -   Example: `Documents/Cline/portfolio-website` for your portfolio
-   **Cline Extension in VS Code:** The Cline extension installed in VS Code.

-   Here's a [tutorial](https://www.youtube.com/watch?v=N4td-fKhsOQ) on everything you need to get started.

## Step-by-Step Setup

Follow these steps to get Cline up and running:

1. **Open VS Code:** Launch the VS Code application. If VS Code shows "Running extensions might...", click "Allow".

2. **Open Your Cline Folder:** In VS Code, open the Cline folder you created in Documents.

3. **Navigate to Extensions:** Click on the Extensions icon in the Activity Bar on the side of VS Code.

4. **Search for 'Cline':** In the Extensions search bar, type "Cline".

5. **Install the Extension:** Click the "Install" button next to the Cline extension.

6. **Open Cline:** Once installed, you can open Cline in a few ways:
    - Click the Cline icon in the Activity Bar.
    - Use the command palette (`CMD/CTRL + Shift + P`) and type "Cline: Open In New Tab" to open Cline as a tab in your editor. This is recommended for a better view.
    - **Troubleshooting:** If you don't see the Cline icon, try restarting VS Code.
    - **What You'll See:** You should see the Cline chat window appear in your VS Code editor.

![gettingStartedVsCodeCline](https://github.com/user-attachments/assets/622b4bb7-859b-4c2e-b87b-c12e3eabefb8)

## Setting up OpenRouter API Key

Now that you have Cline installed, you'll need to set up your OpenRouter API key to use Cline's full capabilities.

1.  **Get your OpenRouter API Key:**
    -   [Get your OpenRouter API Key](https://openrouter.ai/)
2.  **Input Your OpenRouter API Key:**
    -   Navigate to the settings button in the Cline extension.
    -   Input your OpenRouter API key.
    -   Select your preferred API model.
        -   **Recommended Models for Coding:**
            -   `anthropic/claude-3.5-sonnet`: Most used for coding tasks.
            -   `google/gemini-2.0-flash-exp:free`: A free option for coding.
            -   `deepseek/deepseek-chat`: SUPER CHEAP, almost as good as 3.5 sonnet
        -   [OpenRouter Model Rankings](https://openrouter.ai/rankings/programming)

## Your First Interaction with Cline

Now you're ready to start building with Cline. Let's create your first project folder and build something! Copy and paste the following prompt into the Cline chat window:

```
Hey Cline! Could you help me create a new project folder called "hello-world" in my Cline directory and make a simple webpage that says "Hello World" in big blue text?
```

**What You'll See:** Cline will help you create the project folder and set up your first webpage.

## Tips for Working with Cline

-   **Ask Questions:** If you're unsure about something, don't hesitate to ask Cline!
-   **Use Screenshots:** Cline can understand images, so feel free to use screenshots to show him what you're working on.
-   **Copy and Paste Errors:** If you encounter errors, copy and paste the error messages into Cline's chat. This will help him understand the issue and provide a solution.
-   **Speak Plainly:** Cline is designed to understand plain, non-technical language. Feel free to describe your ideas in your own words, and Cline will translate them into code.

## FAQs

-   **What is the Terminal?** The terminal is a text-based interface for interacting with your computer. It allows you to run commands to perform various tasks, such as installing packages, running scripts, and managing files. Cline uses the terminal to execute commands and interact with your development environment.
-   **How Does the Codebase Work?** (This section will be expanded based on common questions from new coders)

## Still Struggling?

Feel free to contact me, and I'll help you get started with Cline.

nick | 608-558-2410

Join our Discord community: [https://discord.gg/cline](https://discord.gg/cline)
