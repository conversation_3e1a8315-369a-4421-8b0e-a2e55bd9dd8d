syntax = "proto3";

package cline;
option java_package = "bot.cline.proto";
option java_multiple_files = true;

import "common.proto";

// Service for model-related operations
service ModelsService {
  // Fetches available models from Ollama
  rpc getOllamaModels(StringRequest) returns (StringArray);
  // Fetches available models from LM Studio
  rpc getLmStudioModels(StringRequest) returns (StringArray);
  // Fetches available models from VS Code LM API
  rpc getVsCodeLmModels(EmptyRequest) returns (VsCodeLmModelsArray);
  // Refreshes and returns OpenRouter models
  rpc refreshOpenRouterModels(EmptyRequest) returns (OpenRouterCompatibleModelInfo);
  // Refreshes and returns OpenAI models
  rpc refreshOpenAiModels(OpenAiModelsRequest) returns (StringArray);
  // Refreshes and returns Requesty models
  rpc refreshRequestyModels(EmptyRequest) returns (OpenRouterCompatibleModelInfo);
  // Subscribe to OpenRouter models updates
  rpc subscribeToOpenRouterModels(EmptyRequest) returns (stream OpenRouterCompatibleModelInfo);
  // Updates API configuration
  rpc updateApiConfigurationProto(UpdateApiConfigurationRequest) returns (Empty);
}

// List of VS Code LM models
message VsCodeLmModelsArray {
  repeated LanguageModelChatSelector models = 1;
}

// Structure representing a language model chat selector
message LanguageModelChatSelector {
  optional string vendor = 1;
  optional string family = 2;
  optional string version = 3;
  optional string id = 4;
}

// Price tier for tiered pricing models
message PriceTier {
  int32 token_limit = 1;  // Upper limit (inclusive) of input tokens for this price
  double price = 2;       // Price per million tokens for this tier
}

// Thinking configuration for models that support thinking/reasoning
message ThinkingConfig {
  optional int32 max_budget = 1;                    // Max allowed thinking budget tokens
  optional double output_price = 2;                 // Output price per million tokens when budget > 0
  repeated PriceTier output_price_tiers = 3;        // Optional: Tiered output price when budget > 0
}

// Model tier for tiered pricing structures
message ModelTier {
  int32 context_window = 1;
  optional double input_price = 2;
  optional double output_price = 3;
  optional double cache_writes_price = 4;
  optional double cache_reads_price = 5;
}

// For OpenRouterCompatibleModelInfo structure in OpenRouterModels
message OpenRouterModelInfo {
  optional int32 max_tokens = 1;
  optional int32 context_window = 2;
  optional bool supports_images = 3;
  bool supports_prompt_cache = 4;
  optional double input_price = 5;
  optional double output_price = 6;
  optional double cache_writes_price = 7;
  optional double cache_reads_price = 8;
  optional string description = 9;
  optional ThinkingConfig thinking_config = 10;
  optional bool supports_global_endpoint = 11;
  repeated ModelTier tiers = 12;
}

// Shared response message for model information
message OpenRouterCompatibleModelInfo {
  map<string, OpenRouterModelInfo> models = 1;
}

// Request for fetching OpenAI models
message OpenAiModelsRequest {
  Metadata metadata = 1;
  string base_url = 2;
  string api_key = 3;
}

// Request for updating API configuration
message UpdateApiConfigurationRequest {
  Metadata metadata = 1;
  ModelsApiConfiguration api_configuration = 2;
}

// API Provider enumeration
enum ApiProvider {
  ANTHROPIC = 0;
  OPENROUTER = 1;
  BEDROCK = 2;
  VERTEX = 3;
  OPENAI = 4;
  OLLAMA = 5;
  LMSTUDIO = 6;
  GEMINI = 7;
  GEMINI_CLI = 8;
  OPENAI_NATIVE = 9;
  REQUESTY = 10;
  TOGETHER = 11;
  DEEPSEEK = 12;
  QWEN = 13;
  DOUBAO = 14;
  MISTRAL = 15;
  VSCODE_LM = 16;
  CLINE = 17;
  LITELLM = 18;
  NEBIUS = 19;
  FIREWORKS = 20;
  ASKSAGE = 21;
  XAI = 22;
  SAMBANOVA = 23;
  CEREBRAS = 24;
  SAPAICORE = 25;
  CLAUDE_CODE = 26;
}

// Model info for OpenAI-compatible models
message OpenAiCompatibleModelInfo {
  optional int32 max_tokens = 1;
  optional int32 context_window = 2;
  optional bool supports_images = 3;
  bool supports_prompt_cache = 4;
  optional double input_price = 5;
  optional double output_price = 6;
  optional ThinkingConfig thinking_config = 7;
  optional bool supports_global_endpoint = 8;
  optional double cache_writes_price = 9;
  optional double cache_reads_price = 10;
  optional string description = 11;
  repeated ModelTier tiers = 12;
  optional double temperature = 13;
  optional bool is_r1_format_required = 14;
}

// Model info for LiteLLM models
message LiteLLMModelInfo {
  optional int32 max_tokens = 1;
  optional int32 context_window = 2;
  optional bool supports_images = 3;
  bool supports_prompt_cache = 4;
  optional double input_price = 5;
  optional double output_price = 6;
  optional ThinkingConfig thinking_config = 7;
  optional bool supports_global_endpoint = 8;
  optional double cache_writes_price = 9;
  optional double cache_reads_price = 10;
  optional string description = 11;
  repeated ModelTier tiers = 12;
  optional double temperature = 13;
}

// Main ApiConfiguration message
message ModelsApiConfiguration {
  // From ApiHandlerOptions (excluding onRetryAttempt function)
  optional string api_model_id = 1;
  optional string api_key = 2;
  optional string cline_api_key = 3;
  optional string task_id = 4;
  optional string lite_llm_base_url = 5;
  optional string lite_llm_model_id = 6;
  optional string lite_llm_api_key = 7;
  optional bool lite_llm_use_prompt_cache = 8;
  map<string, string> open_ai_headers = 9;
  optional LiteLLMModelInfo lite_llm_model_info = 10;
  optional string anthropic_base_url = 11;
  optional string open_router_api_key = 12;
  optional string open_router_model_id = 13;
  optional OpenRouterModelInfo open_router_model_info = 14;
  optional string open_router_provider_sorting = 15;
  optional string aws_access_key = 16;
  optional string aws_secret_key = 17;
  optional string aws_session_token = 18;
  optional string aws_region = 19;
  optional bool aws_use_cross_region_inference = 20;
  optional bool aws_bedrock_use_prompt_cache = 21;
  optional bool aws_use_profile = 22;
  optional string aws_profile = 23;
  optional string aws_bedrock_endpoint = 24;
  optional bool aws_bedrock_custom_selected = 25;
  optional string aws_bedrock_custom_model_base_id = 26;
  optional string vertex_project_id = 27;
  optional string vertex_region = 28;
  optional string open_ai_base_url = 29;
  optional string open_ai_api_key = 30;
  optional string open_ai_model_id = 31;
  optional OpenAiCompatibleModelInfo open_ai_model_info = 32;
  optional string ollama_model_id = 33;
  optional string ollama_base_url = 34;
  optional string ollama_api_options_ctx_num = 35;
  optional string lm_studio_model_id = 36;
  optional string lm_studio_base_url = 37;
  optional string gemini_api_key = 38;
  optional string gemini_base_url = 39;
  optional string open_ai_native_api_key = 40;
  optional string deep_seek_api_key = 41;
  optional string requesty_api_key = 42;
  optional string requesty_model_id = 43;
  optional OpenRouterModelInfo requesty_model_info = 44;
  optional string together_api_key = 45;
  optional string together_model_id = 46;
  optional string fireworks_api_key = 47;
  optional string fireworks_model_id = 48;
  optional int32 fireworks_model_max_completion_tokens = 49;
  optional int32 fireworks_model_max_tokens = 50;
  optional string qwen_api_key = 51;
  optional string doubao_api_key = 52;
  optional string mistral_api_key = 53;
  optional string azure_api_version = 54;
  optional LanguageModelChatSelector vs_code_lm_model_selector = 55;
  optional string qwen_api_line = 56;
  optional string nebius_api_key = 57;
  optional string asksage_api_url = 58;
  optional string asksage_api_key = 59;
  optional string xai_api_key = 60;
  optional int32 thinking_budget_tokens = 61;
  optional string reasoning_effort = 62;
  optional string sambanova_api_key = 63;
  optional string cerebras_api_key = 64;
  optional int32 request_timeout_ms = 65;
  optional ApiProvider api_provider = 66;
  repeated string favorited_model_ids = 67;
  optional string sap_ai_core_client_id = 68;
  optional string sap_ai_core_client_secret = 69;
  optional string sap_ai_resource_group = 70;
  optional string sap_ai_core_token_url = 71;
  optional string sap_ai_core_base_url = 72;
  optional string claude_code_path = 73;
  optional string gemini_cli_oauth_path = 74;
  optional string gemini_cli_project_id = 75;
}
