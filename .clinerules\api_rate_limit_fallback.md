---
description: Defines the fallback strategy when API rate limits are encountered during LLM calls.
globs: src/core/task/index.ts
alwaysApply: true
---

- **Detect API Rate Limit Errors:**
  - When an API request to the primary Language Model (LLM) provider fails due to a rate limit error (e.g., HTTP 429 status code or specific provider error messages), trigger the fallback mechanism.

- **Prioritize Taskmaster Tool Calls:**
  - **Action:** Before retrying the LLM API call, evaluate if the current task can be advanced by using an available Taskmaster MCP tool.
  - **Logic:**
    - Analyze the current task context and the `next_task` information from Taskmaster.
    - Determine if any `tm` tool (e.g., `get_tasks`, `get_task`, `set_task_status`, `update_subtask`) can be used to make progress without requiring a new LLM generation.
    - If a relevant Taskmaster tool is identified, construct the necessary arguments for that tool based on the current task state and execute it.
  - **Outcome:** If a Taskmaster tool successfully executes and advances the task, the system should re-evaluate the task state and proceed.

- **Fallback to Direct LLM Interaction (Input/Output LL):**
  - **Action:** If no suitable Taskmaster tool can be used, or if the Taskmaster tool execution also fails, attempt a direct interaction with the LLM.
  - **Logic:**
    - The current prompt (the user's last input and any accumulated context) should be directly passed to the LLM as input.
    - The raw output from the LLM should be treated as the response, bypassing the standard content block parsing and tool execution flow for that specific turn. This is the "input and output LL" part of the request.
    - This mode is intended for scenarios where the LLM can provide a textual response or guidance even under rate limit pressure, without needing to invoke complex tools.
  - **Outcome:** The LLM's raw response is presented to the user, and the system re-evaluates the next steps.

- **Re-evaluation and Retry:**
  - After attempting either a Taskmaster tool call or a direct LLM interaction, the system should re-evaluate the overall task progress.
  - If the rate limit persists, the process may repeat, or a user prompt for manual intervention may be triggered after a certain number of retries.
