# Overview
This document outlines the requirements for an automated model fallback system within Cline. Currently, when an API request to a language model is rate-limited, the task is interrupted, and the user is prompted to retry manually. This process is inefficient and disrupts the development workflow.

This new system will automatically handle rate-limit errors by switching to a predefined backup model, ensuring that tasks can continue seamlessly without user intervention. This will improve the reliability and user experience of <PERSON><PERSON>, especially during long or intensive tasks.

# Core Features
- **Configuration-Driven Fallback**: A JSON configuration file (`model-fallback-config.json`) will define an ordered list of models. The first model is the primary, and the subsequent entries are the fallbacks.
- **Rate-Limit Detection**: The system will specifically detect HTTP `429` status codes and known rate-limit error messages from the Gemini and Gemini CLI API providers.
- **Automated Model Switching**: Upon detecting a rate-limit error, <PERSON><PERSON> will automatically switch to the next model in the configured list and retry the API request.
- **User Notifications**: The user will be informed via a message in the chat when a rate limit is encountered and a fallback model is being used.

# User Experience
- **User Persona**: A software developer using <PERSON>line for coding assistance.
- **Key User Flow**:
  1. The user initiates a task.
  2. <PERSON><PERSON> makes an API request to the primary model (e.g., `gemini-2.5-pro` from Gemini CLI).
  3. The API returns a rate-limit error.
  4. Cline posts a message to the UI: "Primary model is rate-limited. Switching to fallback: `gemini/gemini-2.5-pro` (Gemini API)."
  5. If `gemini/gemini-2.5-pro` also rate-limits, Cline posts: "Fallback model `gemini/gemini-2.5-pro` rate-limited. Switching to next fallback: `gemini/gemini-2.5-flash` (Gemini API)."
  6. If `gemini/gemini-2.5-flash` also rate-limits, Cline posts: "Fallback model `gemini/gemini-2.5-flash` rate-limited. Switching to next fallback: `gemini-2.5-flash` (Gemini CLI)."
  5. Cline automatically retries the request with the fallback model.
  6. The task continues without requiring any action from the user.
- **Exhaustion Scenario**: If all models in the fallback chain are rate-limited, the user will be notified and prompted to manually retry the last attempted model, preserving the "human-in-the-loop" control.

# Technical Architecture
- **Configuration File**: A new file, `model-fallback-config.json`, will be created in the project's root directory. It will contain a JSON array of model identifier strings. These identifiers will determine which Gemini provider (Gemini API or Gemini CLI) is used for each model in the fallback chain.
- **Controller (`src/core/controller/index.ts`)**: This class will be modified to:
  - Read and parse `model-fallback-config.json` on startup.
  - Store the model fallback chain in its state.
  - Track the index of the currently active model.
  - Provide a method to advance to the next model in the chain.
- **Task (`src/core/task/index.ts`)**: The `attemptApiRequest` method will be updated to:
  - Include a `catch` block that specifically checks for rate-limit errors from Gemini providers.
  - When a rate-limit error is detected, it will call the `Controller` to switch to the next model and then recursively call itself to retry the request.
  - It will maintain a record of failed models for the current request to prevent infinite loops.

# Development Roadmap
- **MVP Requirements**:
  1. Create the `model-fallback-config.json` file with a default sequence of Gemini 2.5 models, including both Gemini CLI and Gemini API models.
  2. Implement the logic in the `Controller` to load and manage the model chain from the configuration file.
  3. Implement the rate-limit detection and automated fallback loop within the `Task` class.
  4. Add the user notification system to inform the user of model switches.
- **Future Enhancements**:
  - Develop a UI in the settings panel to allow users to configure the fallback chain without manually editing the JSON file.
  - Extend the rate-limit detection and fallback logic to support other API providers beyond Gemini.
  - Collect telemetry on model failure rates to potentially suggest more optimal fallback configurations to the user.

# Logical Dependency Chain
1. **Create `model-fallback-config.json`**: This is the foundation of the feature, defining the unified fallback sequence for both Gemini API and Gemini CLI models.
2. **Update `Controller`**: The `Controller` must be able to read and manage the unified configuration before the `Task` class can use it.
3. **Implement `Task` Logic**: The core fallback and retry logic is implemented in the `Task` class, which depends on the `Controller` for state management and dynamically instantiates the correct `ApiHandler` (Gemini API or Gemini CLI) based on the model ID.
4. **Implement User Notifications**: The final step is to ensure the user is informed about the automated actions being taken, including model switches between different Gemini providers.

# Risks and Mitigations
- **Risk**: The system could misinterpret a different API error as a rate-limit error, causing an unnecessary model switch.
- **Mitigation**: The error detection logic will be highly specific, targeting only HTTP `429` status codes and error messages explicitly indicating a rate limit.
- **Risk**: A misconfiguration or logic error could lead to an infinite retry loop.
- **Mitigation**: The `Task` class will keep track of the models it has already attempted for a given API request and will not retry a model that has already failed in the current sequence.
